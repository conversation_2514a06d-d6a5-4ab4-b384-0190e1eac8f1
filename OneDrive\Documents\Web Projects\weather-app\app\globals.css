@import "tailwindcss";

:root {
  --primary: #735187;
  --foreground: #ffffff;
  --secondary: #417ab4;
  --tertiary: #f4d9ff;
  --secondary-foreground: #1e1a20;
  box-sizing: border-box;
}

@theme inline {
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-tertiary: var(--tertiary);
  --color-secondary-foreground: var(--secondary-foreground);
}

*,
::before,
::after {
  box-sizing: border-box;
}

body {
  background: var(--primary);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
  display: flex;
  flex-direction: column;
}
