{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/api/cities/route.ts"], "sourcesContent": ["export async function GET(req: Request): Promise<Response> {\r\n  try {\r\n    const { searchParams } = new URL(req.url);\r\n    const query = searchParams.get(\"query\");\r\n\r\n    // Validate required parameters\r\n    if (!query || query.length < 2) {\r\n      return new Response(\r\n        JSON.stringify({\r\n          error: \"Query parameter must be at least 2 characters\",\r\n        }),\r\n        {\r\n          status: 400,\r\n          headers: { \"Content-Type\": \"application/json\" },\r\n        }\r\n      );\r\n    }\r\n\r\n    // Validate API key\r\n    const apiKey =\r\n      process.env.WEATHER_API_KEY || process.env.NEXT_PUBLIC_WEATHER_API_KEY;\r\n    if (!apiKey) {\r\n      console.error(\"Weather API key not configured\");\r\n      return new Response(\r\n        JSON.stringify({\r\n          error: \"City search service temporarily unavailable\",\r\n        }),\r\n        {\r\n          status: 500,\r\n          headers: { \"Content-Type\": \"application/json\" },\r\n        }\r\n      );\r\n    }\r\n\r\n    // Fetch cities from OpenWeatherMap Geocoding API\r\n    const url = `https://api.openweathermap.org/geo/1.0/direct?q=${encodeURIComponent(\r\n      query\r\n    )}&limit=5&appid=${apiKey}`;\r\n    const response = await fetch(url);\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({}));\r\n      return new Response(JSON.stringify(errorData), {\r\n        status: response.status,\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n      });\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    return new Response(JSON.stringify(data), {\r\n      status: 200,\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"Cache-Control\": \"public, max-age=600\", // Cache for 10 minutes\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Cities API error:\", error);\r\n    return new Response(JSON.stringify({ error: \"Internal server error\" }), {\r\n      status: 500,\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n    });\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,eAAe,IAAI,GAAY;IACpC,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,IAAI,GAAG;QACxC,MAAM,QAAQ,aAAa,GAAG,CAAC;QAE/B,+BAA+B;QAC/B,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,GAAG;YAC9B,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBACb,OAAO;YACT,IACA;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;QAEA,mBAAmB;QACnB,MAAM,SACJ,QAAQ,GAAG,CAAC,eAAe;QAC7B,uCAAa;;QAWb;QAEA,iDAAiD;QACjD,MAAM,MAAM,CAAC,gDAAgD,EAAE,mBAC7D,OACA,eAAe,EAAE,QAAQ;QAC3B,MAAM,WAAW,MAAM,MAAM;QAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC,YAAY;gBAC7C,QAAQ,SAAS,MAAM;gBACvB,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC,OAAO;YACxC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;YACnB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC;YAAE,OAAO;QAAwB,IAAI;YACtE,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;IACF;AACF", "debugId": null}}]}