{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/api/weather/route.ts"], "sourcesContent": ["export async function GET(req: Request): Promise<Response> {\r\n  try {\r\n    const { searchParams } = new URL(req.url);\r\n    const city = searchParams.get(\"city\");\r\n\r\n    // Validate required parameters\r\n    if (!city) {\r\n      return new Response(\r\n        JSON.stringify({ error: \"City parameter is required\" }),\r\n        {\r\n          status: 400,\r\n          headers: { \"Content-Type\": \"application/json\" },\r\n        }\r\n      );\r\n    }\r\n\r\n    // Validate API key\r\n    const apiKey =\r\n      process.env.WEATHER_API_KEY || process.env.NEXT_PUBLIC_WEATHER_API_KEY;\r\n    if (!apiKey) {\r\n      console.error(\"Weather API key not configured\");\r\n      return new Response(\r\n        JSON.stringify({ error: \"Weather service temporarily unavailable\" }),\r\n        {\r\n          status: 500,\r\n          headers: { \"Content-Type\": \"application/json\" },\r\n        }\r\n      );\r\n    }\r\n\r\n    // Fetch weather data from OpenWeatherMap API\r\n    const url = `https://api.openweathermap.org/data/2.5/weather?q=${encodeURIComponent(\r\n      city\r\n    )}&appid=${apiKey}&units=metric`;\r\n    const response = await fetch(url);\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({}));\r\n      return new Response(JSON.stringify(errorData), {\r\n        status: response.status,\r\n        headers: { \"Content-Type\": \"application/json\" },\r\n      });\r\n    }\r\n\r\n    const data = await response.json();\r\n\r\n    return new Response(JSON.stringify(data), {\r\n      status: 200,\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        \"Cache-Control\": \"public, max-age=300\", // Cache for 5 minutes\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error(\"Weather API error:\", error);\r\n    return new Response(JSON.stringify({ error: \"Internal server error\" }), {\r\n      status: 500,\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n    });\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAO,eAAe,IAAI,GAAY;IACpC,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,IAAI,GAAG;QACxC,MAAM,OAAO,aAAa,GAAG,CAAC;QAE9B,+BAA+B;QAC/B,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;gBAAE,OAAO;YAA6B,IACrD;gBACE,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QAEJ;QAEA,mBAAmB;QACnB,MAAM,SACJ,QAAQ,GAAG,CAAC,eAAe;QAC7B,uCAAa;;QASb;QAEA,6CAA6C;QAC7C,MAAM,MAAM,CAAC,kDAAkD,EAAE,mBAC/D,MACA,OAAO,EAAE,OAAO,aAAa,CAAC;QAChC,MAAM,WAAW,MAAM,MAAM;QAE7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;YACvD,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC,YAAY;gBAC7C,QAAQ,SAAS,MAAM;gBACvB,SAAS;oBAAE,gBAAgB;gBAAmB;YAChD;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAEhC,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC,OAAO;YACxC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB;YACnB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,IAAI,SAAS,KAAK,SAAS,CAAC;YAAE,OAAO;QAAwB,IAAI;YACtE,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAmB;QAChD;IACF;AACF", "debugId": null}}]}