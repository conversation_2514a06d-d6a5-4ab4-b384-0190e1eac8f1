<!DOCTYPE html>
<html>
<head>
    <title>Weather App Favicon Generator</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            padding: 20px; 
            background: #f0f8ff;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .favicon-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .icon-option {
            width: 64px;
            height: 64px;
            border: 2px solid #ddd;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .icon-option:hover {
            border-color: #4682B4;
            transform: scale(1.1);
        }
        .icon-option.selected {
            border-color: #4682B4;
            background: #e6f3ff;
        }
        canvas {
            border: 1px solid #ddd;
            margin: 10px;
        }
        button {
            background: #4682B4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #36648B;
        }
        .download-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌤️ Weather App Favicon Generator</h1>
        
        <h3>Choose your weather icon:</h3>
        <div class="favicon-preview">
            <div class="icon-option" data-emoji="☀️">☀️</div>
            <div class="icon-option" data-emoji="🌤️">🌤️</div>
            <div class="icon-option" data-emoji="⛅">⛅</div>
            <div class="icon-option" data-emoji="🌦️">🌦️</div>
            <div class="icon-option" data-emoji="🌧️">🌧️</div>
            <div class="icon-option" data-emoji="⛈️">⛈️</div>
            <div class="icon-option" data-emoji="🌨️">🌨️</div>
            <div class="icon-option" data-emoji="🌡️">🌡️</div>
        </div>

        <h3>Preview:</h3>
        <canvas id="favicon16" width="16" height="16"></canvas>
        <canvas id="favicon32" width="32" height="32"></canvas>
        <canvas id="favicon64" width="64" height="64"></canvas>
        <canvas id="apple180" width="180" height="180"></canvas>

        <div class="download-section">
            <h3>Download your favicons:</h3>
            <button onclick="downloadFavicon(16)">Download 16x16 (favicon.ico)</button>
            <button onclick="downloadFavicon(32)">Download 32x32</button>
            <button onclick="downloadFavicon(64)">Download 64x64</button>
            <button onclick="downloadFavicon(180)">Download 180x180 (Apple Touch)</button>
            
            <p><strong>Instructions:</strong></p>
            <ol>
                <li>Select a weather emoji above</li>
                <li>Download the favicon files</li>
                <li>Rename the 16x16 file to <code>favicon.ico</code></li>
                <li>Rename the 180x180 file to <code>apple-touch-icon.png</code></li>
                <li>Place them in your <code>public</code> folder</li>
            </ol>
        </div>
    </div>

    <script>
        let selectedEmoji = '🌤️';

        // Icon selection
        document.querySelectorAll('.icon-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.icon-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                selectedEmoji = this.dataset.emoji;
                updatePreviews();
            });
        });

        // Set default selection
        document.querySelector('[data-emoji="🌤️"]').classList.add('selected');

        function updatePreviews() {
            drawFavicon('favicon16', 16);
            drawFavicon('favicon32', 32);
            drawFavicon('favicon64', 64);
            drawFavicon('apple180', 180);
        }

        function drawFavicon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Background gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#87CEEB');
            gradient.addColorStop(1, '#4682B4');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // Draw emoji
            ctx.font = `${size * 0.7}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(selectedEmoji, size / 2, size / 2);
        }

        function downloadFavicon(size) {
            const canvas = document.getElementById(size === 16 ? 'favicon16' : 
                                                 size === 32 ? 'favicon32' : 
                                                 size === 64 ? 'favicon64' : 'apple180');
            
            const link = document.createElement('a');
            link.download = size === 16 ? 'favicon.ico' : 
                           size === 180 ? 'apple-touch-icon.png' : 
                           `favicon-${size}x${size}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }

        // Initialize previews
        updatePreviews();
    </script>
</body>
</html>
