{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/ui/WeatherForm.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState, useRef } from \"react\";\r\nimport { useWeather } from \"../context/WeatherContext\";\r\nimport { MapPinIcon, ChevronRightIcon } from \"@heroicons/react/24/outline\";\r\n\r\ninterface CityOption {\r\n  name: string;\r\n  country: string;\r\n  state?: string;\r\n}\r\n\r\nconst WeatherForm = () => {\r\n  const [city, setCity] = useState(\"Salt Lake City, Utah, US\");\r\n  const [suggestions, setSuggestions] = useState<CityOption[]>([]);\r\n  const [showSuggestions, setShowSuggestions] = useState(false);\r\n  const [isSearching, setIsSearching] = useState(false);\r\n  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);\r\n  const { setWeather } = useWeather();\r\n\r\n  useEffect(() => {\r\n    getWeather(city);\r\n  }, []); // eslint-disable-line react-hooks/exhaustive-deps\r\n\r\n  // Cleanup timeout on unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      if (searchTimeoutRef.current) {\r\n        clearTimeout(searchTimeoutRef.current);\r\n      }\r\n    };\r\n  }, []);\r\n\r\n  const searchCities = async (query: string): Promise<void> => {\r\n    if (query.length < 2) {\r\n      setSuggestions([]);\r\n      setShowSuggestions(false);\r\n      return;\r\n    }\r\n\r\n    setIsSearching(true);\r\n    try {\r\n      const response = await fetch(\r\n        `/api/cities?query=${encodeURIComponent(query)}`\r\n      );\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n\r\n      const data = await response.json();\r\n\r\n      const cityOptions: CityOption[] = data.map((item: any) => ({\r\n        name: item.name,\r\n        country: item.country,\r\n        state: item.state,\r\n      }));\r\n\r\n      setSuggestions(cityOptions);\r\n      setShowSuggestions(cityOptions.length > 0);\r\n    } catch (error) {\r\n      console.error(\"Error searching cities:\", error);\r\n      setSuggestions([]);\r\n      setShowSuggestions(false);\r\n    } finally {\r\n      setIsSearching(false);\r\n    }\r\n  };\r\n\r\n  const debouncedSearch = (query: string): void => {\r\n    if (searchTimeoutRef.current) {\r\n      clearTimeout(searchTimeoutRef.current);\r\n    }\r\n\r\n    searchTimeoutRef.current = setTimeout(() => {\r\n      searchCities(query);\r\n    }, 300);\r\n  };\r\n\r\n  const getWeather = async (inputCity: string): Promise<void> => {\r\n    try {\r\n      const response = await fetch(\r\n        \"/api/weather?city=\" + encodeURIComponent(inputCity)\r\n      );\r\n\r\n      if (!response.ok) {\r\n        throw new Error(`HTTP error! status: ${response.status}`);\r\n      }\r\n\r\n      const weatherData = await response.json();\r\n\r\n      if (weatherData.cod === \"404\") {\r\n        console.warn(`City not found: ${inputCity}`);\r\n        return;\r\n      }\r\n\r\n      if (!weatherData.main || !weatherData.weather?.[0]) {\r\n        throw new Error(\"Invalid weather data received\");\r\n      }\r\n\r\n      const weather = {\r\n        city: weatherData.name,\r\n        temperature: weatherData.main.temp,\r\n        windSpeed: weatherData.wind.speed,\r\n        windDirection: weatherData.wind.deg,\r\n        humidity: weatherData.main.humidity,\r\n        weather: weatherData.weather[0].main,\r\n        maxTemp: weatherData.main.temp_max,\r\n        minTemp: weatherData.main.temp_min,\r\n        feelsLike: weatherData.main.feels_like,\r\n      };\r\n\r\n      setWeather(weather);\r\n    } catch (error) {\r\n      console.error(\"Error fetching weather:\", error);\r\n      // Could add user-facing error handling here\r\n    }\r\n  };\r\n\r\n  const handleCitySelect = (selectedCity: CityOption): void => {\r\n    const cityName = selectedCity.state\r\n      ? `${selectedCity.name}, ${selectedCity.state}, ${selectedCity.country}`\r\n      : `${selectedCity.name}, ${selectedCity.country}`;\r\n\r\n    setCity(cityName);\r\n    setShowSuggestions(false);\r\n    setSuggestions([]);\r\n    getWeather(cityName);\r\n  };\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {\r\n    const value = e.target.value;\r\n    setCity(value);\r\n    debouncedSearch(value);\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent): Promise<void> => {\r\n    e.preventDefault();\r\n    setShowSuggestions(false);\r\n    await getWeather(city);\r\n  };\r\n\r\n  return (\r\n    <div className=\"relative\">\r\n      <form onSubmit={handleSubmit} className=\"flex flex-row gap-4 w-full\">\r\n        <div className=\"flex flex-row gap-2 bg-foreground rounded-xl text-primary p-4 shadow-lg flex-1 w-full\">\r\n          <MapPinIcon className=\"h-6 w-6 text-primary md:h-8 md:w-8\" />\r\n          <input\r\n            type=\"text\"\r\n            name=\"city\"\r\n            placeholder=\"City...\"\r\n            value={city}\r\n            className=\"text-xl outline-none flex-grow md:text-2xl bg-transparent\"\r\n            onChange={handleInputChange}\r\n            onFocus={() =>\r\n              city.length >= 2 &&\r\n              suggestions.length > 0 &&\r\n              setShowSuggestions(true)\r\n            }\r\n            onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}\r\n          />\r\n          {isSearching && (\r\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-primary\"></div>\r\n          )}\r\n          <button type=\"submit\" className=\"text-primary\">\r\n            <ChevronRightIcon className=\"h-6 w-6 text-primary md:h-8 md:w-8\" />\r\n          </button>\r\n        </div>\r\n      </form>\r\n\r\n      {showSuggestions && suggestions.length > 0 && (\r\n        <div className=\"absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto\">\r\n          {suggestions.map((suggestion, index) => (\r\n            <div\r\n              key={`${suggestion.name}-${suggestion.country}-${index}`}\r\n              className=\"px-4 py-3 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0\"\r\n              onClick={() => handleCitySelect(suggestion)}\r\n            >\r\n              <div className=\"flex items-center gap-2\">\r\n                <MapPinIcon className=\"h-4 w-4 text-gray-500\" />\r\n                <div>\r\n                  <div className=\"font-medium text-gray-900\">\r\n                    {suggestion.name}\r\n                  </div>\r\n                  <div className=\"text-sm text-gray-500\">\r\n                    {suggestion.state ? `${suggestion.state}, ` : \"\"}\r\n                    {suggestion.country}\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default WeatherForm;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;;;AAJA;;;;AAYA,MAAM,cAAc;;IAClB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IACvD,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD;IAEhC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;QACb;gCAAG,EAAE,GAAG,kDAAkD;IAE1D,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;yCAAO;oBACL,IAAI,iBAAiB,OAAO,EAAE;wBAC5B,aAAa,iBAAiB,OAAO;oBACvC;gBACF;;QACF;gCAAG,EAAE;IAEL,MAAM,eAAe,OAAO;QAC1B,IAAI,MAAM,MAAM,GAAG,GAAG;YACpB,eAAe,EAAE;YACjB,mBAAmB;YACnB;QACF;QAEA,eAAe;QACf,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,CAAC,kBAAkB,EAAE,mBAAmB,QAAQ;YAGlD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,MAAM,cAA4B,KAAK,GAAG,CAAC,CAAC,OAAc,CAAC;oBACzD,MAAM,KAAK,IAAI;oBACf,SAAS,KAAK,OAAO;oBACrB,OAAO,KAAK,KAAK;gBACnB,CAAC;YAED,eAAe;YACf,mBAAmB,YAAY,MAAM,GAAG;QAC1C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,eAAe,EAAE;YACjB,mBAAmB;QACrB,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,iBAAiB,OAAO,EAAE;YAC5B,aAAa,iBAAiB,OAAO;QACvC;QAEA,iBAAiB,OAAO,GAAG,WAAW;YACpC,aAAa;QACf,GAAG;IACL;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MACrB,uBAAuB,mBAAmB;YAG5C,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,cAAc,MAAM,SAAS,IAAI;YAEvC,IAAI,YAAY,GAAG,KAAK,OAAO;gBAC7B,QAAQ,IAAI,CAAC,CAAC,gBAAgB,EAAE,WAAW;gBAC3C;YACF;YAEA,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,OAAO,EAAE,CAAC,EAAE,EAAE;gBAClD,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,UAAU;gBACd,MAAM,YAAY,IAAI;gBACtB,aAAa,YAAY,IAAI,CAAC,IAAI;gBAClC,WAAW,YAAY,IAAI,CAAC,KAAK;gBACjC,eAAe,YAAY,IAAI,CAAC,GAAG;gBACnC,UAAU,YAAY,IAAI,CAAC,QAAQ;gBACnC,SAAS,YAAY,OAAO,CAAC,EAAE,CAAC,IAAI;gBACpC,SAAS,YAAY,IAAI,CAAC,QAAQ;gBAClC,SAAS,YAAY,IAAI,CAAC,QAAQ;gBAClC,WAAW,YAAY,IAAI,CAAC,UAAU;YACxC;YAEA,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,4CAA4C;QAC9C;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,WAAW,aAAa,KAAK,GAC/B,GAAG,aAAa,IAAI,CAAC,EAAE,EAAE,aAAa,KAAK,CAAC,EAAE,EAAE,aAAa,OAAO,EAAE,GACtE,GAAG,aAAa,IAAI,CAAC,EAAE,EAAE,aAAa,OAAO,EAAE;QAEnD,QAAQ;QACR,mBAAmB;QACnB,eAAe,EAAE;QACjB,WAAW;IACb;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,QAAQ;QACR,gBAAgB;IAClB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,mBAAmB;QACnB,MAAM,WAAW;IACnB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAK,UAAU;gBAAc,WAAU;0BACtC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,sNAAA,CAAA,aAAU;4BAAC,WAAU;;;;;;sCACtB,6LAAC;4BACC,MAAK;4BACL,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,WAAU;4BACV,UAAU;4BACV,SAAS,IACP,KAAK,MAAM,IAAI,KACf,YAAY,MAAM,GAAG,KACrB,mBAAmB;4BAErB,QAAQ,IAAM,WAAW,IAAM,mBAAmB,QAAQ;;;;;;wBAE3D,6BACC,6LAAC;4BAAI,WAAU;;;;;;sCAEjB,6LAAC;4BAAO,MAAK;4BAAS,WAAU;sCAC9B,cAAA,6LAAC,kOAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;YAKjC,mBAAmB,YAAY,MAAM,GAAG,mBACvC,6LAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,6LAAC;wBAEC,WAAU;wBACV,SAAS,IAAM,iBAAiB;kCAEhC,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,sNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDACZ,WAAW,IAAI;;;;;;sDAElB,6LAAC;4CAAI,WAAU;;gDACZ,WAAW,KAAK,GAAG,GAAG,WAAW,KAAK,CAAC,EAAE,CAAC,GAAG;gDAC7C,WAAW,OAAO;;;;;;;;;;;;;;;;;;;uBAZpB,GAAG,WAAW,IAAI,CAAC,CAAC,EAAE,WAAW,OAAO,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;;AAsBtE;GAxLM;;QAMmB,oIAAA,CAAA,aAAU;;;KAN7B;uCA0LS", "debugId": null}}, {"offset": {"line": 276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/app/ui/wind_speed/WindMPH.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { useWeather } from \"../../context/WeatherContext\";\r\n\r\nconst WindMPH = () => {\r\n  const [tempWind, setTempWind] = useState<number>(0);\r\n  const { weather } = useWeather();\r\n  const [windSpeed, setWindSpeed] = useState<number>(0);\r\n\r\n  //animation\r\n  const duration = 2000; // 2 seconds\r\n  const maxSteps = 20; // minimum number of steps for smooth animation\r\n\r\n  useEffect(() => {\r\n    setWindSpeed(Math.round(weather?.windSpeed ?? 0));\r\n  }, [weather]);\r\n\r\n  useEffect(() => {\r\n    setTempWind(0);\r\n\r\n    const steps = Math.max(maxSteps, windSpeed);\r\n    const stepDuration = duration / steps;\r\n\r\n    const timer = setInterval(() => {\r\n      setTempWind((prevTempWind) => {\r\n        if (prevTempWind >= windSpeed) {\r\n          clearInterval(timer);\r\n          return windSpeed;\r\n        }\r\n        return prevTempWind + 1;\r\n      });\r\n    }, stepDuration);\r\n\r\n    return () => {\r\n      clearInterval(timer);\r\n    };\r\n  }, [windSpeed]);\r\n\r\n  return <>{tempWind}</>;\r\n};\r\n\r\nexport default WindMPH;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,UAAU;;IACd,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,aAAU,AAAD;IAC7B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEnD,WAAW;IACX,MAAM,WAAW,MAAM,YAAY;IACnC,MAAM,WAAW,IAAI,+CAA+C;IAEpE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,aAAa,KAAK,KAAK,CAAC,SAAS,aAAa;QAChD;4BAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,YAAY;YAEZ,MAAM,QAAQ,KAAK,GAAG,CAAC,UAAU;YACjC,MAAM,eAAe,WAAW;YAEhC,MAAM,QAAQ;2CAAY;oBACxB;mDAAY,CAAC;4BACX,IAAI,gBAAgB,WAAW;gCAC7B,cAAc;gCACd,OAAO;4BACT;4BACA,OAAO,eAAe;wBACxB;;gBACF;0CAAG;YAEH;qCAAO;oBACL,cAAc;gBAChB;;QACF;4BAAG;QAAC;KAAU;IAEd,qBAAO;kBAAG;;AACZ;GAnCM;;QAEgB,oIAAA,CAAA,aAAU;;;KAF1B;uCAqCS", "debugId": null}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/node_modules/%40heroicons/react/24/outline/esm/MapPinIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction MapPinIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z\"\n  }), /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(MapPinIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,WAAW,EAClB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL,IAAI,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC3C,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Documents/Web%20Projects/weather-app/node_modules/%40heroicons/react/24/outline/esm/ChevronRightIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction ChevronRightIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"m8.25 4.5 7.5 7.5-7.5 7.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(ChevronRightIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,iBAAiB,EACxB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,EAAE,MAAM;IACP,OAAO,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE;uCACnC", "ignoreList": [0], "debugId": null}}]}